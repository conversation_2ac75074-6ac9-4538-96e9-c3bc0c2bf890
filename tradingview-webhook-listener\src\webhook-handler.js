class WebhookHandler {
    constructor(configManager, moneyManager, tradingExecutor, marketDataService, positionManager, logger) {
        this.configManager = configManager;
        this.moneyManager = moneyManager;
        this.tradingExecutor = tradingExecutor;
        this.marketDataService = marketDataService;
        this.positionManager = positionManager;
        this.logger = logger;

        this.signalsReceived = 0;
        this.tradesExecuted = 0;
        this.tradesSkipped = 0;
        this.lastSignalTime = null;
        this.tradesHistory = [];
    }

    async handleWebhook(payload) {
        const startTime = Date.now();
        this.signalsReceived++;
        this.lastSignalTime = new Date().toISOString();

        this.logger.info('Webhook received', { payload, signalNumber: this.signalsReceived });

        try {
            // Validate payload
            const validationResult = this.validatePayload(payload);
            if (!validationResult.valid) {
                throw new Error(`Invalid payload: ${validationResult.error}`);
            }

            // Check if bot is active
            const config = this.configManager.getConfig();
            if (!config.botActive) {
                this.logger.info('Bot is inactive, ignoring signal');
                return {
                    success: false,
                    message: 'Bot is inactive',
                    signal: payload,
                    timestamp: this.lastSignalTime
                };
            }

            // Check if system is properly configured
            if (!this.configManager.isConfigured()) {
                throw new Error('System not properly configured. Please set API keys and money management settings.');
            }

            // Ignore all 'close' signals from TradingView webhook
            if (payload.trade && payload.trade.toLowerCase() === 'close') {
                this.logger.info('Ignoring close signal from TradingView webhook as per configuration');
                return {
                    success: true,
                    message: 'Close signals are ignored. Close mechanism handled by TP/SL.',
                    signal: payload,
                    timestamp: this.lastSignalTime
                };
            }

            // Process the signal
            // New TradingView format: 'buy' = Open Long, 'sell' = Open Short
            // No mapping needed as processSignal will handle it directly

            const processedSignal = this.processSignal(payload);

            // Validate execution conditions
            const executionValidation = await this.validateExecution(processedSignal);
            if (!executionValidation.valid) {
                this.tradesSkipped++;
                this.logger.info('Trade skipped due to validation failure', {
                    reason: executionValidation.reason,
                    details: executionValidation.details
                });

                const skippedRecord = {
                    id: `skipped_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                    timestamp: this.lastSignalTime,
                    signal: payload,
                    processedSignal,
                    positionSize: 'Skipped',
                    tradeResult: { executionTime: 0 },
                    reason: executionValidation.reason,
                    details: executionValidation.details,
                    success: false,
                    skipped: true
                };

                this.tradesHistory.unshift(skippedRecord);
                if (this.tradesHistory.length > 100) {
                    this.tradesHistory = this.tradesHistory.slice(0, 100);
                }

                return {
                    success: false,
                    message: `Trade skipped: ${executionValidation.reason}`,
                    signal: payload,
                    processedSignal,
                    reason: executionValidation.reason,
                    details: executionValidation.details,
                    skipped: true,
                    timestamp: this.lastSignalTime
                };
            }

            // Calculate position size using money management
            const positionSize = await this.moneyManager.calculatePositionSize(processedSignal);

            // Execute the trade with timeout
            const executionStartTime = Date.now();

            const tradeResult = await Promise.race([
                this.tradingExecutor.executeTrade({
                    ...processedSignal,
                    quantity: positionSize.toString()
                }),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('Execution timeout')), config.maxExecutionTime)
                )
            ]);

            this.tradesExecuted++;
            const executionTime = Date.now() - startTime;

            // Add position to monitoring if trade was successful and it's an open order
            let positionAdded = null;
            if (tradeResult.success && processedSignal.action === 'open') {
                try {
                    const currentPrice = await this.marketDataService.getCurrentPrice(processedSignal.symbol);
                    positionAdded = await this.positionManager.addPosition({
                        symbol: processedSignal.symbol,
                        direction: processedSignal.direction, // Use actual direction from signal
                        entryPrice: currentPrice.price,
                        quantity: parseFloat(positionSize),
                        tradeId: `trade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                        requestId: tradeResult.requestId
                    });

                    this.logger.info('Position added to SL/TP monitoring', {
                        positionId: positionAdded?.id,
                        symbol: processedSignal.symbol,
                        entryPrice: currentPrice.price,
                        quantity: positionSize
                    });
                } catch (error) {
                    this.logger.error('Failed to add position for SL/TP monitoring', {
                        error: error.message,
                        tradeResult
                    });
                }
            }

            // Record trade in history
            const tradeRecord = {
                id: `trade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                timestamp: this.lastSignalTime,
                signal: payload,
                processedSignal,
                positionSize,
                tradeResult,
                executionTime,
                success: tradeResult.success,
                positionId: positionAdded?.id,
                slTpEnabled: this.configManager.getConfig().slTpEnabled
            };

            this.tradesHistory.unshift(tradeRecord); // Add to beginning
            
            // Keep only last 100 trades
            if (this.tradesHistory.length > 100) {
                this.tradesHistory = this.tradesHistory.slice(0, 100);
            }

            this.logger.info('Trade executed', { 
                tradeRecord,
                totalExecutionTime: executionTime
            });

            // Handle different result types
            let message;
            if (tradeResult.skipped) {
                message = `Trade skipped: ${tradeResult.skipReason}`;
            } else if (tradeResult.success) {
                message = 'Trade executed successfully';
            } else {
                message = 'Trade execution failed';
            }

            return {
                success: tradeResult.success,
                skipped: tradeResult.skipped || false,
                skipReason: tradeResult.skipReason,
                message: message,
                signal: payload,
                processedSignal,
                positionSize,
                tradeResult,
                executionTime,
                tradeId: tradeRecord.id,
                timestamp: this.lastSignalTime
            };

        } catch (error) {
            const executionTime = Date.now() - startTime;
            
            this.logger.error('Webhook processing failed', { 
                error: error.message, 
                stack: error.stack,
                payload,
                executionTime
            });

            // Record failed trade with more details for frontend display
            const failedTradeRecord = {
                id: `failed_trade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                timestamp: this.lastSignalTime,
                signal: payload,
                processedSignal: processedSignal || { orderType: 'Failed to process' },
                positionSize: 'N/A',
                tradeResult: { executionTime },
                error: error.message,
                executionTime,
                success: false
            };

            this.tradesHistory.unshift(failedTradeRecord);
            
            if (this.tradesHistory.length > 100) {
                this.tradesHistory = this.tradesHistory.slice(0, 100);
            }

            return {
                success: false,
                error: error.message,
                signal: payload,
                executionTime,
                tradeId: failedTradeRecord.id,
                timestamp: this.lastSignalTime
            };
        }
    }

    validatePayload(payload) {
        if (!payload || typeof payload !== 'object') {
            return { valid: false, error: 'Payload must be an object' };
        }

        // Required fields for new TradingView format
        const requiredFields = ['symbol', 'trade', 'last_price'];
        for (const field of requiredFields) {
            if (!payload[field]) {
                return { valid: false, error: `Missing required field: ${field}` };
            }
        }

        // Leverage is now optional (will use default from config if not provided)

        // Validate symbol (only TRUUSDT supported)
        if (payload.symbol !== 'TRUUSDT') {
            return { valid: false, error: `Unsupported symbol: ${payload.symbol}. Only TRUUSDT is supported.` };
        }

        // Validate trade action - New TradingView format supports 'buy' and 'sell'
        const validActions = [
            'buy', 'sell', // New TradingView format
            'open', 'close', 'long', 'short', 'exit', // Legacy support
            'open_long', 'open_short', 'close_long', 'close_short',
            'buy_close', 'sell_open'
        ];
        if (!validActions.includes(payload.trade.toLowerCase())) {
            return { valid: false, error: `Invalid trade action: ${payload.trade}. Supported actions: ${validActions.join(', ')}.` };
        }

        // Validate price
        const price = parseFloat(payload.last_price);
        if (isNaN(price) || price <= 0) {
            return { valid: false, error: `Invalid price: ${payload.last_price}` };
        }

        // Validate leverage if provided (support both spellings)
        const leverageValue = payload.leverage || payload.leverege;
        if (leverageValue) {
            const leverage = parseFloat(leverageValue);
            if (isNaN(leverage) || leverage <= 0 || leverage > 100) {
                return { valid: false, error: `Invalid leverage: ${leverageValue}. Must be between 1 and 100.` };
            }
        }

        return { valid: true };
    }

    processSignal(payload) {
        const { symbol, trade, last_price } = payload;
        const leverage = payload.leverage || payload.leverege; // Support both spellings
        const config = this.configManager.getConfig();

        // Determine order type and direction based on trade field
        let orderType, action, direction;
        const tradeType = trade.toLowerCase();

        switch (tradeType) {
            case 'buy':
            case 'open':
            case 'long':
            case 'open_long':
                orderType = 'Open Long';
                action = 'open';
                direction = 'long';
                break;
            case 'sell':
            case 'open_short':
            case 'short':
            case 'sell_open':
                orderType = 'Open Short';
                action = 'open';
                direction = 'short';
                break;
            case 'close':
            case 'exit':
            case 'close_long':
                orderType = 'Close Long';
                action = 'close';
                direction = 'long';
                break;
            case 'close_short':
            case 'buy_close':
                orderType = 'Close Short';
                action = 'close';
                direction = 'short';
                break;
            default:
                throw new Error(`Unsupported trade type: ${trade}`);
        }

        return {
            symbol: symbol.toUpperCase(),
            orderType,
            action,
            direction,
            price: parseFloat(last_price),
            leverage: leverage ? parseFloat(leverage) : config.defaultLeverage,
            originalSignal: payload
        };
    }

    getTotalSignalsReceived() {
        return this.signalsReceived;
    }

    async validateExecution(processedSignal) {
        const config = this.configManager.getConfig();

        try {
            // Get current market price
            const currentPriceData = await this.marketDataService.getCurrentPrice(processedSignal.symbol);
            const currentPrice = currentPriceData.price;
            const expectedPrice = processedSignal.price;

            // Validate price difference
            const priceValidation = this.marketDataService.validatePriceDifference(
                currentPrice,
                expectedPrice,
                config.maxPriceDifference
            );

            if (!priceValidation.valid) {
                return {
                    valid: false,
                    reason: 'Price difference too large',
                    details: {
                        currentPrice,
                        expectedPrice,
                        difference: priceValidation.difference,
                        maxAllowed: config.maxPriceDifference
                    }
                };
            }

            return {
                valid: true,
                currentPrice,
                expectedPrice,
                priceDifference: priceValidation.difference
            };

        } catch (error) {
            return {
                valid: false,
                reason: 'Market data validation failed',
                details: {
                    error: error.message
                }
            };
        }
    }

    getTotalSignalsReceived() {
        return this.signalsReceived;
    }

    getTotalTradesExecuted() {
        return this.tradesExecuted;
    }

    getTotalTradesSkipped() {
        return this.tradesSkipped;
    }

    getLastSignalTime() {
        return this.lastSignalTime;
    }

    getTradesHistory() {
        return this.tradesHistory;
    }

    // Get statistics
    getStatistics() {
        const successfulTrades = this.tradesHistory.filter(t => t.success && !t.skipped).length;
        const failedTrades = this.tradesHistory.filter(t => !t.success && !t.skipped).length;
        const skippedTrades = this.tradesHistory.filter(t => t.skipped).length;

        const executedTrades = this.tradesHistory.filter(t => !t.skipped);
        const avgExecutionTime = executedTrades.length > 0
            ? executedTrades.reduce((sum, t) => sum + (t.executionTime || 0), 0) / executedTrades.length
            : 0;

        return {
            totalSignalsReceived: this.signalsReceived,
            totalTradesExecuted: this.tradesExecuted,
            totalTradesSkipped: this.tradesSkipped,
            successfulTrades,
            failedTrades,
            skippedTrades,
            successRate: executedTrades.length > 0 ? (successfulTrades / executedTrades.length * 100).toFixed(2) : 0,
            skipRate: this.signalsReceived > 0 ? (skippedTrades / this.signalsReceived * 100).toFixed(2) : 0,
            averageExecutionTime: Math.round(avgExecutionTime),
            lastSignalTime: this.lastSignalTime,
            totalTradesInHistory: this.tradesHistory.length,
            activePositions: this.positionManager.getActivePositions().length
        };
    }

    // Reset statistics (for testing)
    resetStatistics() {
        this.signalsReceived = 0;
        this.tradesExecuted = 0;
        this.lastSignalTime = null;
        this.tradesHistory = [];
    }
}

module.exports = WebhookHandler;
