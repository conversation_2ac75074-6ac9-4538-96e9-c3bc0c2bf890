const axios = require('axios');

class TradingExecutor {
    constructor(configManager) {
        this.configManager = configManager;
        this.mexcTraderServiceUrl = 'http://localhost:3000'; // MEXC Futures Trader service
        this.ready = false;
        this.lastHealthCheck = null;
        this.cachedBalance = null;
        this.lastBalanceUpdate = null;
        this.balanceUpdateInterval = 5 * 60 * 1000; // 5 minutes
        this.isExecutingTrade = false; // Flag to prevent balance checks during trades
    }

    async checkServiceHealth() {
        try {
            const response = await axios.get(`${this.mexcTraderServiceUrl}/health`, {
                timeout: 5000
            });

            this.ready = response.data.status === 'healthy';
            this.lastHealthCheck = new Date().toISOString();

            return {
                healthy: this.ready,
                service: response.data.service,
                timestamp: response.data.timestamp
            };
        } catch (error) {
            this.ready = false;
            this.lastHealthCheck = new Date().toISOString();

            throw new Error(`MEXC Trader Service not available: ${error.message}`);
        }
    }

    async getBalance(forceRefresh = false) {
        try {
            // 🧪 TESTING MODE: Return mock balance for comprehensive testing
            const mockBalance = {
                success: true,
                balance: 2.29,
                currency: 'USDT',
                raw: '2.29 USDT',
                timestamp: new Date().toISOString(),
                cached: false,
                mockMode: true
            };

            console.log('🧪 TESTING MODE: Using mock balance of 2.29 USDT');
            return mockBalance;

            // Original balance fetching code (commented out for testing)
            /*
            // Don't fetch balance during trade execution unless forced
            if (this.isExecutingTrade && !forceRefresh) {
                console.log('⚡ Skipping balance check during trade execution');
                if (this.cachedBalance) {
                    return this.cachedBalance;
                }
            }

            // Use cached balance if recent (within 5 minutes) and not forced
            if (!forceRefresh && this.cachedBalance && this.lastBalanceUpdate) {
                const age = Date.now() - this.lastBalanceUpdate;
                if (age < this.balanceUpdateInterval) {
                    console.log(`💰 Using cached balance: ${this.cachedBalance.balance} USDT (${Math.round(age/1000)}s old)`);
                    return this.cachedBalance;
                }
            }

            console.log('🔄 Fetching fresh balance from frontend...');
            const refreshParam = forceRefresh ? '?refresh=true' : '';
            const response = await axios.get(`${this.mexcTraderServiceUrl}/balance${refreshParam}`, {
                timeout: 10000
            });

            const balanceData = {
                success: response.data.success,
                balance: response.data.balance || 0,
                currency: response.data.currency || 'USDT',
                raw: response.data.raw,
                timestamp: response.data.timestamp,
                cached: false
            };

            // Cache the balance if successful
            if (balanceData.success) {
                this.cachedBalance = balanceData;
                this.lastBalanceUpdate = Date.now();
                console.log(`💰 Balance cached: ${balanceData.balance} ${balanceData.currency}`);
            }

            return balanceData;
            */
        } catch (error) {
            console.error('Failed to get balance:', error.message);

            // Return mock balance as fallback during testing
            return {
                success: true,
                balance: 2.29,
                currency: 'USDT',
                error: error.message,
                timestamp: new Date().toISOString(),
                mockMode: true,
                fallback: true
            };
        }
    }

    isReady() {
        return this.ready;
    }

    async executeTrade(tradeRequest) {
        const startTime = Date.now();

        try {
            // Set trade execution flag to prevent balance checks during trade
            this.isExecutingTrade = true;
            console.log(`🎯 Starting trade execution: ${tradeRequest.orderType}`);

            // Validate trade request
            this.validateTradeRequest(tradeRequest);

            // Check if service is ready
            if (!this.ready) {
                await this.checkServiceHealth();
            }

            // Prepare the trade payload for MEXC Futures Trader service
            const tradePayload = {
                orderType: tradeRequest.orderType,
                quantity: tradeRequest.quantity
            };

            console.log(`Executing trade: ${tradeRequest.orderType} with quantity ${tradeRequest.quantity}`);

            // Execute the trade via MEXC Futures Trader service
            const response = await axios.post(`${this.mexcTraderServiceUrl}/trade`, tradePayload, {
                timeout: 30000, // 30 seconds timeout for trade execution
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'TradingView-Webhook-Listener/1.0.0'
                }
            });
            
            const executionTime = Date.now() - startTime;
            
            // Process the response
            const result = {
                success: response.data.success,
                executionTime: response.data.executionTime || executionTime,
                totalServiceTime: executionTime,
                verified: response.data.verified,
                targetAchieved: response.data.targetAchieved,
                orderType: response.data.orderType,
                quantity: response.data.quantity,
                port: response.data.port,
                requestId: response.data.requestId,
                timestamp: response.data.timestamp,
                tradeRequest,
                serviceResponse: response.data
            };
            
            if (!response.data.success) {
                result.error = response.data.error || 'Trade execution failed';
            }
            
            return result;
            
        } catch (error) {
            const executionTime = Date.now() - startTime;
            
            let errorMessage = 'Trade execution failed';
            let errorDetails = {};
            
            if (error.response) {
                // HTTP error response from MEXC Trader service
                errorMessage = error.response.data?.error || error.response.statusText;
                errorDetails = {
                    status: error.response.status,
                    statusText: error.response.statusText,
                    data: error.response.data
                };
            } else if (error.request) {
                // Network error
                errorMessage = 'Unable to connect to MEXC Trader service';
                errorDetails = {
                    code: error.code,
                    message: error.message
                };
            } else {
                // Other error
                errorMessage = error.message;
            }
            
            return {
                success: false,
                error: errorMessage,
                errorDetails,
                executionTime,
                tradeRequest,
                timestamp: new Date().toISOString()
            };
        } finally {
            // Always clear trade execution flag
            this.isExecutingTrade = false;
            console.log('🏁 Trade execution completed, balance checks re-enabled');
        }
    }

    validateTradeRequest(tradeRequest) {
        if (!tradeRequest || typeof tradeRequest !== 'object') {
            throw new Error('Invalid trade request: must be an object');
        }
        
        if (!tradeRequest.orderType) {
            throw new Error('Invalid trade request: orderType is required');
        }
        
        const validOrderTypes = ['Open Long', 'Open Short', 'Close Long', 'Close Short'];
        if (!validOrderTypes.includes(tradeRequest.orderType)) {
            throw new Error(`Invalid order type: ${tradeRequest.orderType}. Must be one of: ${validOrderTypes.join(', ')}`);
        }
        
        if (!tradeRequest.quantity) {
            throw new Error('Invalid trade request: quantity is required');
        }
        
        const quantity = parseFloat(tradeRequest.quantity);
        if (isNaN(quantity) || quantity <= 0) {
            throw new Error(`Invalid quantity: ${tradeRequest.quantity}. Must be a positive number`);
        }
        
        // Additional validation for symbol (if provided)
        if (tradeRequest.symbol && tradeRequest.symbol !== 'TRUUSDT') {
            throw new Error(`Unsupported symbol: ${tradeRequest.symbol}. Only TRUUSDT is supported`);
        }
    }

    async executeBatchTrades(tradeRequests) {
        if (!Array.isArray(tradeRequests) || tradeRequests.length === 0) {
            throw new Error('Invalid batch request: must be a non-empty array');
        }
        
        if (tradeRequests.length > 10) {
            throw new Error('Batch size too large: maximum 10 trades per batch');
        }
        
        const startTime = Date.now();
        const results = [];
        
        try {
            // Check service health before batch execution
            await this.checkServiceHealth();
            
            // Prepare batch payload
            const batchPayload = {
                orders: tradeRequests.map(req => ({
                    orderType: req.orderType,
                    quantity: req.quantity
                }))
            };
            
            console.log(`Executing batch of ${tradeRequests.length} trades`);
            
            // Execute batch via MEXC Futures Trader service
            const response = await axios.post(`${this.mexcTraderServiceUrl}/trade/batch`, batchPayload, {
                timeout: 60000, // 60 seconds for batch execution
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'TradingView-Webhook-Listener/1.0.0'
                }
            });
            
            const executionTime = Date.now() - startTime;
            
            return {
                success: response.data.success,
                totalOrders: response.data.totalOrders,
                successCount: response.data.successCount,
                failureCount: response.data.failureCount,
                results: response.data.results,
                executionTime,
                timestamp: response.data.timestamp
            };
            
        } catch (error) {
            const executionTime = Date.now() - startTime;
            
            return {
                success: false,
                error: error.response?.data?.error || error.message,
                executionTime,
                timestamp: new Date().toISOString()
            };
        }
    }

    async getServiceInfo() {
        try {
            const response = await axios.get(`${this.mexcTraderServiceUrl}/info`, {
                timeout: 5000
            });
            
            return response.data;
        } catch (error) {
            throw new Error(`Failed to get service info: ${error.message}`);
        }
    }

    async testConnection() {
        try {
            const healthCheck = await this.checkServiceHealth();
            const serviceInfo = await this.getServiceInfo();
            
            return {
                success: true,
                message: 'Successfully connected to MEXC Futures Trader service',
                health: healthCheck,
                serviceInfo,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    // Get trading statistics from the service
    async getTradingStatistics() {
        try {
            // This would require additional endpoints in the MEXC Trader service
            // For now, return basic info
            return {
                serviceUrl: this.mexcTraderServiceUrl,
                ready: this.ready,
                lastHealthCheck: this.lastHealthCheck,
                supportedOrderTypes: ['Open Long', 'Open Short', 'Close Long', 'Close Short'],
                supportedSymbols: ['TRUUSDT']
            };
        } catch (error) {
            throw new Error(`Failed to get trading statistics: ${error.message}`);
        }
    }

    // Update service URL (for testing or different environments)
    updateServiceUrl(newUrl) {
        this.mexcTraderServiceUrl = newUrl;
        this.ready = false; // Reset ready status
    }

    // Get current service status
    getStatus() {
        return {
            serviceUrl: this.mexcTraderServiceUrl,
            ready: this.ready,
            lastHealthCheck: this.lastHealthCheck,
            timestamp: new Date().toISOString()
        };
    }
}

module.exports = TradingExecutor;
