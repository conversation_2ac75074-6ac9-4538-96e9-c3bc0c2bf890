const { chromium } = require('playwright');
const axios = require('axios');

class SingleBrowserTrader {
    constructor(port = 9223) {
        this.browser = null;
        this.page = null;
        this.port = port;
        this.telegramBotToken = '**********************************************';
        this.telegramChatId = '142183523';
        this.lastBalance = null;
        this.lastBalanceUpdate = null;
    }

    async connect() {
        try {
            console.log(`🔗 Connecting to browser on port ${this.port}...`);
            
            this.browser = await chromium.connectOverCDP(`http://localhost:${this.port}`);
            const contexts = this.browser.contexts();
            
            if (contexts.length === 0) {
                throw new Error('No browser contexts found');
            }
            
            const pages = contexts[0].pages();
            if (pages.length === 0) {
                throw new Error('No pages found in browser context');
            }
            
            this.page = pages[0];
            console.log('✅ Connected to browser successfully');
            
            return true;
        } catch (error) {
            console.error('❌ Failed to connect to browser:', error.message);
            return false;
        }
    }

    async executeTrade(orderType, quantity = '0.0001') {
        const startTime = Date.now();
        
        try {
            console.log(`⚡ Executing ${orderType} with quantity ${quantity}...`);

            // Check for login requirement first
            const loginRequired = await this.checkLoginRequired();
            if (loginRequired) {
                await this.sendTelegramAlert('🚨 MEXC Login Required', 
                    `MEXC session expired. Please login manually.\nPort: ${this.port}\nOrder: ${orderType}`);
                throw new Error('MEXC login required. Session expired.');
            }

            // Step 1: Select appropriate tab (Open or Close)
            await this.selectTab(orderType);

            // Step 2: Fill quantity with proven working method
            await this.fillQuantityWithErrorHandling(orderType, quantity);

            // Step 3: Click order button with error handling
            await this.clickOrderButtonWithErrorHandling(orderType);

            // Step 4: Quick error check (no popup waiting for MEXC futures)
            await this.quickErrorCheck(orderType);

            const executionTime = Date.now() - startTime;
            
            // Quick verification
            const verified = await this.quickVerifySuccess(orderType);

            console.log(`⚡ ${orderType} completed in ${executionTime}ms`);
            console.log(`🎉 Verified: ${verified ? '✅ YES' : '❌ NO'}`);

            return {
                success: verified,
                executionTime,
                verified,
                orderType,
                quantity,
                targetAchieved: executionTime < 2000,
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            const executionTime = Date.now() - startTime;
            console.error(`❌ ${orderType} failed after ${executionTime}ms:`, error.message);
            
            return {
                success: false,
                executionTime,
                error: error.message,
                orderType,
                quantity,
                targetAchieved: false,
                timestamp: new Date().toISOString()
            };
        }
    }

    async selectTab(orderType) {
        try {
            console.log(`📋 Selecting tab for ${orderType}...`);
            
            let tabSelector;
            if (orderType.includes('Open')) {
                // Select Open tab
                tabSelector = 'span[data-testid="contract-trade-order-form-tab-open"]';
                console.log('🔄 Selecting Open tab...');
            } else if (orderType.includes('Close')) {
                // Select Close tab
                tabSelector = 'span[data-testid="contract-trade-order-form-tab-close"]';
                console.log('🔄 Selecting Close tab...');
            } else {
                throw new Error(`Unknown order type: ${orderType}`);
            }

            // Wait for tab to be visible and click it
            await this.page.waitForSelector(tabSelector, { timeout: 3000 });
            await this.page.click(tabSelector);
            
            // Wait a moment for tab content to load
            await this.page.waitForTimeout(300);
            
            console.log('✅ Tab selected successfully');
        } catch (error) {
            console.error('❌ Failed to select tab:', error.message);
            throw error;
        }
    }

    async fillQuantityWithErrorHandling(orderType, quantity) {
        console.log(`🔢 Filling quantity: ${quantity}...`);

        try {
            // First attempt - no cleanup
            await this.fillQuantity(quantity);
            return;
        } catch (error) {
            console.log('⚠️ First quantity attempt failed, clearing quantity fields...');

            try {
                // First error: Clean quantity only
                await this.clearQuantityField();
                await this.fillQuantity(quantity);
                return;
            } catch (secondError) {
                console.log('⚠️ Second quantity attempt failed, closing popups...');

                // Second error: Close popups then try again
                await this.closePersistentPopups();
                await this.page.waitForTimeout(500);
                await this.fillQuantity(quantity);
            }
        }
    }

    async fillQuantity(quantity = '0.0001') {
        // Check if we're in close mode and handle differently
        const isCloseMode = await this.isInCloseMode();
        if (isCloseMode) {
            console.log('🔄 Detected close mode - using close-specific quantity strategies');
            return await this.fillQuantityForClose(quantity);
        }

        const strategies = [
            // Strategy 1: XPath following Quantity(USDT) - Fast version
            async () => {
                const input = this.page.locator('text=Quantity(USDT) >> xpath=following::input[1]').first();
                if (await input.isVisible({ timeout: 500 })) {
                    // Try direct fill first (fastest)
                    await input.click();
                    await input.fill(quantity);

                    // Verify it worked
                    const value = await input.inputValue();
                    if (value === quantity) {
                        console.log('✅ Strategy 1 (XPath Quantity(USDT)) succeeded');
                        return true;
                    }
                }
                return false;
            },

            // Strategy 2: XPath following Quantity - Fast version
            async () => {
                const input = this.page.locator('text=Quantity >> xpath=following::input[1]').first();
                if (await input.isVisible({ timeout: 300 })) {
                    const type = await input.getAttribute('type');
                    if (type !== 'checkbox') {
                        await input.click();
                        await input.fill(quantity);

                        const value = await input.inputValue();
                        if (value === quantity) {
                            console.log('✅ Strategy 2 (XPath Quantity) succeeded');
                            return true;
                        }
                    }
                }
                return false;
            }
        ];

        for (const strategy of strategies) {
            try {
                if (await strategy()) {
                    return;
                }
            } catch (error) {
                console.log(`Strategy failed: ${error.message}`);
            }
        }

        throw new Error('All quantity filling strategies failed');
    }

    async isInCloseMode() {
        try {
            // Check if Close tab is active by looking for the active class
            const closeTab = this.page.locator('span[data-testid="contract-trade-order-form-tab-close"]').first();
            const hasActiveClass = await closeTab.getAttribute('class');
            const isActive = hasActiveClass && (hasActiveClass.includes('active') || hasActiveClass.includes('handle_active'));

            console.log(`🔍 Close mode check: ${isActive ? 'YES' : 'NO'} (class: ${hasActiveClass})`);
            return isActive;
        } catch (error) {
            console.log('⚠️ Close mode detection failed, assuming open mode');
            return false;
        }
    }

    async fillQuantityForClose(quantity = '0.0001') {
        console.log(`🔢 Filling quantity for close mode: ${quantity}...`);

        const closeStrategies = [
            // Strategy 1: XPath method for close mode
            async () => {
                const input = this.page.locator('text=Quantity(USDT) >> xpath=following::input[1]').first();
                if (await input.isVisible({ timeout: 300 })) {
                    console.log('✅ Found Quantity(USDT) field in close mode');

                    await input.click({ force: true });
                    await input.fill(quantity);

                    const value = await input.inputValue();
                    if (value === quantity) {
                        console.log('✅ Close mode quantity filled successfully');
                        return true;
                    }
                }
                return false;
            }
        ];

        for (const strategy of closeStrategies) {
            try {
                if (await strategy()) {
                    return;
                }
            } catch (error) {
                console.log(`Close strategy failed: ${error.message}`);
            }
        }

        throw new Error('All close mode quantity strategies failed');
    }

    async clearQuantityField() {
        console.log('🔢 Clearing quantity fields...');

        const clearStrategies = [
            async () => {
                const input = this.page.locator('text=Quantity(USDT) >> xpath=following::input[1]').first();
                if (await input.isVisible({ timeout: 300 })) {
                    await this.clearInputField(input);
                    return true;
                }
                return false;
            }
        ];

        for (const strategy of clearStrategies) {
            try {
                if (await strategy()) {
                    return;
                }
            } catch (error) {
                // Continue to next strategy
            }
        }
    }

    async clearInputField(input) {
        try {
            await input.click();
            await input.selectText();
            await input.press('Delete');
            await input.fill('');
        } catch (error) {
            // Fallback methods
            try {
                await input.click();
                await input.press('Control+a');
                await input.press('Delete');
            } catch (fallbackError) {
                // Final fallback
                await input.fill('');
            }
        }
    }

    async closePersistentPopups() {
        console.log('🔄 Closing persistent popups...');

        const popupCloseSelectors = [
            'button:has-text("×")',
            'button:has-text("Close")',
            '.close-btn',
            '.popup-close',
            '[data-testid*="close"]'
        ];

        for (const selector of popupCloseSelectors) {
            try {
                const element = this.page.locator(selector).first();
                if (await element.isVisible({ timeout: 300 })) {
                    await element.click();
                    console.log(`✅ Closed popup: ${selector}`);
                }
            } catch (error) {
                // Continue to next selector
            }
        }
    }

    async clickOrderButtonWithErrorHandling(orderType) {
        console.log(`🎯 Clicking ${orderType} button with error handling...`);

        try {
            // First attempt
            await this.clickOrderButton(orderType);
            return;
        } catch (error) {
            console.log('⚠️ First button click failed, clearing quantity fields...');

            try {
                // First error: Clean quantity only (might be quantity issue)
                await this.clearQuantityField();
                await this.fillQuantity();
                await this.clickOrderButton(orderType);
                return;
            } catch (secondError) {
                console.log('⚠️ Second button click failed, closing popups...');

                // Second error: Close popups then try again
                await this.closePersistentPopups();
                await this.page.waitForTimeout(500);
                await this.clickOrderButton(orderType);
            }
        }
    }

    async clickOrderButton(orderType) {
        console.log(`🎯 Clicking ${orderType} button...`);

        const buttonStrategies = [
            // Strategy 1: Text-based selectors (most reliable)
            async () => {
                let buttonText;
                if (orderType === 'Open Long') {
                    buttonText = 'Open Long';
                } else if (orderType === 'Open Short') {
                    buttonText = 'Open Short';
                } else if (orderType === 'Close Long') {
                    buttonText = 'Close Long';
                } else if (orderType === 'Close Short') {
                    buttonText = 'Close Short';
                } else {
                    throw new Error(`Unknown order type: ${orderType}`);
                }

                const button = this.page.locator(`button:has-text("${buttonText}")`).first();
                if (await button.isVisible({ timeout: 2000 })) {
                    await button.click();
                    console.log(`✅ Clicked ${buttonText} button`);
                    return true;
                }
                return false;
            },

            // Strategy 2: CSS class-based selectors
            async () => {
                let buttonClass;
                if (orderType === 'Open Long') {
                    buttonClass = '.open-long-btn, .buy-btn, .long-btn';
                } else if (orderType === 'Open Short') {
                    buttonClass = '.open-short-btn, .sell-btn, .short-btn';
                } else if (orderType === 'Close Long') {
                    buttonClass = '.close-long-btn, .close-buy-btn';
                } else if (orderType === 'Close Short') {
                    buttonClass = '.close-short-btn, .close-sell-btn';
                }

                if (buttonClass) {
                    const button = this.page.locator(buttonClass).first();
                    if (await button.isVisible({ timeout: 1000 })) {
                        await button.click();
                        console.log(`✅ Clicked ${orderType} button via class`);
                        return true;
                    }
                }
                return false;
            }
        ];

        for (const strategy of buttonStrategies) {
            try {
                if (await strategy()) {
                    return;
                }
            } catch (error) {
                console.log(`Button strategy failed: ${error.message}`);
            }
        }

        throw new Error(`Failed to click ${orderType} button with all strategies`);
    }

    async quickErrorCheck(orderType) {
        console.log(`🔍 Quick error check for ${orderType}...`);

        // Only check for critical errors that should cause us to skip the trade
        const errorSelectors = [
            'text=Insufficient closeable quantity',
            'text=insufficient closeable quantity',
            'text=Insufficient closeable quantity!',
            'text=No position to close',
            'text=Position not found',
            '[class*="error"]:has-text("closeable")',
            '[class*="warning"]:has-text("closeable")'
        ];

        for (const selector of errorSelectors) {
            try {
                const errorElement = this.page.locator(selector).first();
                if (await errorElement.isVisible({ timeout: 100 })) {
                    const errorText = await errorElement.textContent();
                    console.log(`⚠️ Error detected: "${errorText}"`);

                    // Try to close the error popup quickly
                    const closeButtons = [
                        'button:has-text("OK")',
                        'button:has-text("Close")',
                        'button:has-text("×")',
                        '.close-btn',
                        '[data-testid*="close"]'
                    ];

                    for (const closeBtn of closeButtons) {
                        try {
                            const closeElement = this.page.locator(closeBtn).first();
                            if (await closeElement.isVisible({ timeout: 100 })) {
                                await closeElement.click();
                                console.log('✅ Closed error popup');
                                break;
                            }
                        } catch (e) {
                            // Continue
                        }
                    }

                    // Throw special error to skip this trade
                    throw new Error(`SKIP_TRADE: ${errorText}`);
                }
            } catch (error) {
                if (error.message.startsWith('SKIP_TRADE:')) {
                    throw error; // Re-throw skip errors
                }
                // Continue checking other selectors
            }
        }

        console.log('✅ No critical errors detected');
        return true;
    }

    async checkLoginRequired() {
        try {
            const loginSelectors = [
                'button:has-text("Login")',
                'button:has-text("Log in")',
                'button:has-text("Sign in")',
                'a:has-text("Login")',
                'text=Sign up to get',
                'text=Login'
            ];

            for (const selector of loginSelectors) {
                try {
                    const element = this.page.locator(selector).first();
                    if (await element.isVisible({ timeout: 500 })) {
                        console.log(`🚨 Login required detected: ${selector}`);
                        return true;
                    }
                } catch (error) {
                    // Continue checking
                }
            }

            return false;
        } catch (error) {
            console.log('⚠️ Error checking login status:', error.message);
            return false;
        }
    }

    async sendTelegramAlert(title, message) {
        try {
            const fullMessage = `${title}\n\n${message}\n\nTime: ${new Date().toISOString()}`;
            
            await axios.post(`https://api.telegram.org/bot${this.telegramBotToken}/sendMessage`, {
                chat_id: this.telegramChatId,
                text: fullMessage,
                parse_mode: 'HTML'
            });
            
            console.log('📱 Telegram alert sent successfully');
        } catch (error) {
            console.log('❌ Failed to send Telegram alert:', error.message);
        }
    }

    async quickVerifySuccess(orderType) {
        try {
            await this.page.waitForTimeout(300);
            
            const successSelectors = [
                'text=Order submitted',
                'text=Success',
                'text=Completed',
                '.success',
                '.order-success'
            ];

            for (const selector of successSelectors) {
                try {
                    const element = this.page.locator(selector).first();
                    if (await element.isVisible({ timeout: 200 })) {
                        return true;
                    }
                } catch (error) {
                    // Continue checking
                }
            }

            return true; // Assume success if no error indicators
        } catch (error) {
            console.log('⚠️ Quick verification failed:', error.message);
            return false;
        }
    }

    async fetchBalance() {
        try {
            console.log('💰 Fetching balance from MEXC frontend...');

            if (!this.page) {
                throw new Error('Browser not connected');
            }

            // Try multiple balance selectors
            const balanceSelectors = [
                'span.AssetsItem_num__9eLwJ',
                '.AssetsItem_num__9eLwJ',
                '[class*="AssetsItem_num"]',
                '[class*="assets"] [class*="num"]',
                'span:has-text("USDT")',
                'text=/\\d+\\.\\d+\\s*USDT/'
            ];

            let balanceText = null;
            let usedSelector = null;

            for (const selector of balanceSelectors) {
                try {
                    const element = this.page.locator(selector).first();
                    if (await element.isVisible({ timeout: 1000 })) {
                        balanceText = await element.textContent();
                        usedSelector = selector;
                        console.log(`✅ Found balance with selector: ${selector}`);
                        break;
                    }
                } catch (error) {
                    // Continue to next selector
                }
            }

            if (!balanceText) {
                // Try to find any element containing USDT
                try {
                    const usdtElements = await this.page.locator('text=/\\d+\\.\\d+.*USDT/').all();
                    if (usdtElements.length > 0) {
                        balanceText = await usdtElements[0].textContent();
                        usedSelector = 'USDT pattern match';
                        console.log('✅ Found balance with USDT pattern match');
                    }
                } catch (error) {
                    // Continue
                }
            }

            if (balanceText) {
                // Extract numeric value from text like "‎2.2951 USDT" or "2.2951USDT"
                const balanceMatch = balanceText.match(/[\d,]+\.?\d*/);
                if (balanceMatch) {
                    const balance = parseFloat(balanceMatch[0].replace(/,/g, ''));

                    this.lastBalance = {
                        raw: balanceText.trim(),
                        value: balance,
                        currency: 'USDT',
                        selector: usedSelector,
                        timestamp: new Date().toISOString()
                    };
                    this.lastBalanceUpdate = Date.now();

                    console.log(`💰 Balance fetched: ${balance} USDT`);
                    console.log(`   Raw text: "${balanceText.trim()}"`);
                    console.log(`   Selector: ${usedSelector}`);

                    return this.lastBalance;
                } else {
                    console.log(`⚠️ Could not parse balance from: "${balanceText}"`);
                }
            } else {
                console.log('⚠️ No balance element found');
            }

            return null;

        } catch (error) {
            console.error('❌ Failed to fetch balance:', error.message);
            return null;
        }
    }

    async getBalance(forceRefresh = false) {
        try {
            // Return cached balance if recent (less than 30 seconds old)
            if (!forceRefresh && this.lastBalance && this.lastBalanceUpdate) {
                const age = Date.now() - this.lastBalanceUpdate;
                if (age < 30000) { // 30 seconds
                    console.log(`💰 Using cached balance: ${this.lastBalance.value} USDT (${Math.round(age/1000)}s old)`);
                    return this.lastBalance;
                }
            }

            // Fetch fresh balance
            return await this.fetchBalance();

        } catch (error) {
            console.error('❌ Failed to get balance:', error.message);
            return this.lastBalance; // Return cached balance as fallback
        }
    }

    async disconnect() {
        try {
            if (this.browser) {
                await this.browser.close();
                console.log('✅ Browser connection closed');
            }
        } catch (error) {
            console.log('⚠️ Error closing browser:', error.message);
        }
    }
}

// API Server for Single Browser Trader
const express = require('express');
const cors = require('cors');

class SingleBrowserTraderServer {
    constructor() {
        this.app = express();
        this.trader = new SingleBrowserTrader();
        this.setupMiddleware();
        this.setupRoutes();
    }

    setupMiddleware() {
        this.app.use(cors());
        this.app.use(express.json());
    }

    setupRoutes() {
        // Health check
        this.app.get('/health', (req, res) => {
            res.json({
                status: 'ok',
                service: 'Single Browser Trader',
                port: 9223,
                timestamp: new Date().toISOString()
            });
        });

        // Execute trade
        this.app.post('/execute', async (req, res) => {
            try {
                const { orderType, quantity = '0.0001' } = req.body;

                if (!orderType) {
                    return res.status(400).json({
                        success: false,
                        error: 'orderType is required'
                    });
                }

                // Connect to browser if not connected
                if (!this.trader.page) {
                    const connected = await this.trader.connect();
                    if (!connected) {
                        return res.status(500).json({
                            success: false,
                            error: 'Failed to connect to browser'
                        });
                    }
                }

                const result = await this.trader.executeTrade(orderType, quantity);
                res.json(result);

            } catch (error) {
                res.status(500).json({
                    success: false,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        });

        // Test connection
        this.app.get('/test-connection', async (req, res) => {
            try {
                const connected = await this.trader.connect();
                res.json({
                    success: connected,
                    message: connected ? 'Connected successfully' : 'Connection failed',
                    port: 9223
                });
            } catch (error) {
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Get balance
        this.app.get('/balance', async (req, res) => {
            try {
                const forceRefresh = req.query.refresh === 'true';

                // Connect to browser if not connected
                if (!this.trader.page) {
                    const connected = await this.trader.connect();
                    if (!connected) {
                        return res.status(500).json({
                            success: false,
                            error: 'Failed to connect to browser'
                        });
                    }
                }

                const balance = await this.trader.getBalance(forceRefresh);

                if (balance) {
                    res.json({
                        success: true,
                        balance: balance.value,
                        currency: balance.currency,
                        raw: balance.raw,
                        timestamp: balance.timestamp,
                        selector: balance.selector,
                        cached: !forceRefresh && balance.timestamp
                    });
                } else {
                    res.json({
                        success: false,
                        error: 'Could not fetch balance',
                        balance: 0,
                        currency: 'USDT',
                        timestamp: new Date().toISOString()
                    });
                }

            } catch (error) {
                res.status(500).json({
                    success: false,
                    error: error.message,
                    balance: 0,
                    currency: 'USDT',
                    timestamp: new Date().toISOString()
                });
            }
        });
    }

    start(port = 3001) {
        this.app.listen(port, () => {
            console.log('🚀 Single Browser Trader API Server');
            console.log('===================================');
            console.log(`Server running on port ${port}`);
            console.log(`📊 Health endpoint: http://localhost:${port}/health`);
            console.log(`🎯 Execute endpoint: http://localhost:${port}/execute`);
            console.log(`🔗 Test connection: http://localhost:${port}/test-connection`);
            console.log('⚡ Ready to execute trades on port 9223!');
        });
    }
}

// Start server if run directly
if (require.main === module) {
    const server = new SingleBrowserTraderServer();
    server.start();
}

module.exports = { SingleBrowserTrader, SingleBrowserTraderServer };
