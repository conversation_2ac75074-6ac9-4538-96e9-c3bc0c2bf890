const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const winston = require('winston');
const MexcFuturesTrader = require('./trader');

// Configure logging
const logger = winston.createLogger({
    level: 'info',
    format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
    ),
    defaultMeta: { service: 'mexc-futures-trader' },
    transports: [
        new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
        new winston.transports.File({ filename: 'logs/combined.log' }),
        new winston.transports.Console({
            format: winston.format.combine(
                winston.format.colorize(),
                winston.format.simple()
            )
        })
    ]
});

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json());

// Request logging middleware
app.use((req, res, next) => {
    logger.info(`${req.method} ${req.path}`, { 
        ip: req.ip, 
        userAgent: req.get('User-Agent'),
        body: req.body 
    });
    next();
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'mexc-futures-trader',
        version: '1.0.0'
    });
});

// Balance endpoint
app.get('/balance', async (req, res) => {
    try {
        const forceRefresh = req.query.refresh === 'true';

        logger.info(`Balance request - Force refresh: ${forceRefresh}`);

        // Create trader instance
        const trader = new MexcFuturesTrader(9223);

        // Connect to browser
        const connected = await trader.connectToBrowser();
        if (!connected) {
            return res.status(500).json({
                success: false,
                error: 'Failed to connect to browser',
                balance: 0,
                currency: 'USDT',
                timestamp: new Date().toISOString()
            });
        }

        // Fetch balance
        const balance = await trader.getBalance(forceRefresh);

        let responseData;
        if (balance) {
            logger.info(`Balance fetched: ${balance.value} ${balance.currency}`);

            responseData = {
                success: true,
                balance: balance.value,
                currency: balance.currency,
                raw: balance.raw,
                timestamp: balance.timestamp,
                selector: balance.selector,
                cached: !forceRefresh && balance.timestamp
            };
        } else {
            logger.warn('Could not fetch balance');

            responseData = {
                success: false,
                error: 'Could not fetch balance from frontend',
                balance: 0,
                currency: 'USDT',
                timestamp: new Date().toISOString()
            };
        }

        // Disconnect trader (don't let disconnect errors affect the response)
        try {
            await trader.disconnect();
        } catch (disconnectError) {
            logger.warn('Error disconnecting trader:', disconnectError.message);
        }

        // Send response after disconnect attempt
        res.json(responseData);

    } catch (error) {
        logger.error('Balance endpoint error:', error);

        res.status(500).json({
            success: false,
            error: error.message,
            balance: 0,
            currency: 'USDT',
            timestamp: new Date().toISOString()
        });
    }
});

// Get service info
app.get('/info', (req, res) => {
    res.json({
        service: 'MEXC Futures Trader',
        version: '1.0.0',
        description: 'High-speed MEXC futures trading service with sub-2 second execution',
        supportedOrders: ['Open Long', 'Open Short', 'Close Long', 'Close Short'],
        ports: {
            open: 9222,
            close: 9223
        },
        features: [
            'Optimized quantity field handling',
            'Smart popup management',
            'Error recovery system',
            'Position validation',
            'Sub-2 second execution target'
        ]
    });
});

// Execute trade endpoint
app.post('/trade', async (req, res) => {
    const startTime = Date.now();
    
    try {
        const { orderType, quantity = '0.3600' } = req.body;
        
        // Validate input
        if (!orderType) {
            return res.status(400).json({
                success: false,
                error: 'orderType is required',
                validOrderTypes: ['Open Long', 'Open Short', 'Close Long', 'Close Short']
            });
        }

        const validOrderTypes = ['Open Long', 'Open Short', 'Close Long', 'Close Short'];
        if (!validOrderTypes.includes(orderType)) {
            return res.status(400).json({
                success: false,
                error: `Invalid orderType: ${orderType}`,
                validOrderTypes
            });
        }

        // Validate quantity
        const quantityNum = parseFloat(quantity);
        if (isNaN(quantityNum) || quantityNum <= 0) {
            return res.status(400).json({
                success: false,
                error: 'quantity must be a positive number',
                example: '0.3600'
            });
        }

        // Use single browser on port 9223 for all order types
        const port = 9223;

        logger.info(`Executing trade: ${orderType} with quantity ${quantity} on single browser (port ${port})`);

        // Create trader instance
        const trader = new MexcFuturesTrader(port);
        
        // Connect to browser
        const connected = await trader.connectToBrowser();
        if (!connected) {
            throw new Error(`Failed to connect to browser on port ${port}`);
        }

        // Execute the order
        const result = await trader.executeOrder(orderType, quantity);
        
        const totalTime = Date.now() - startTime;
        
        logger.info(`Trade completed: ${orderType}`, { 
            result, 
            totalTime,
            port 
        });

        res.json({
            ...result,
            totalServiceTime: totalTime,
            port,
            requestId: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
        });

    } catch (error) {
        const totalTime = Date.now() - startTime;
        
        logger.error('Trade execution failed', { 
            error: error.message, 
            stack: error.stack,
            totalTime,
            body: req.body
        });

        res.status(500).json({
            success: false,
            error: error.message,
            totalServiceTime: totalTime,
            timestamp: new Date().toISOString()
        });
    }
});

// Batch trade endpoint (for multiple orders)
app.post('/trade/batch', async (req, res) => {
    const startTime = Date.now();
    
    try {
        const { orders } = req.body;
        
        if (!Array.isArray(orders) || orders.length === 0) {
            return res.status(400).json({
                success: false,
                error: 'orders array is required and must not be empty'
            });
        }

        if (orders.length > 10) {
            return res.status(400).json({
                success: false,
                error: 'Maximum 10 orders per batch request'
            });
        }

        logger.info(`Executing batch trade with ${orders.length} orders`);
        
        const results = [];
        
        for (let i = 0; i < orders.length; i++) {
            const order = orders[i];
            const { orderType, quantity = '0.3600' } = order;
            
            try {
                const port = 9223; // Single browser approach
                const trader = new MexcFuturesTrader(port);
                
                const connected = await trader.connectToBrowser();
                if (!connected) {
                    throw new Error(`Failed to connect to browser on port ${port}`);
                }

                const result = await trader.executeOrder(orderType, quantity);
                results.push({
                    orderIndex: i,
                    ...result
                });

                // Small delay between orders to prevent conflicts
                if (i < orders.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }

            } catch (error) {
                results.push({
                    orderIndex: i,
                    success: false,
                    error: error.message,
                    orderType,
                    quantity
                });
            }
        }

        const totalTime = Date.now() - startTime;
        const successCount = results.filter(r => r.success).length;
        
        logger.info(`Batch trade completed`, { 
            totalOrders: orders.length,
            successCount,
            totalTime
        });

        res.json({
            success: successCount > 0,
            totalOrders: orders.length,
            successCount,
            failureCount: orders.length - successCount,
            results,
            totalServiceTime: totalTime,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        const totalTime = Date.now() - startTime;
        
        logger.error('Batch trade execution failed', { 
            error: error.message, 
            stack: error.stack,
            totalTime
        });

        res.status(500).json({
            success: false,
            error: error.message,
            totalServiceTime: totalTime,
            timestamp: new Date().toISOString()
        });
    }
});

// Error handling middleware
app.use((error, req, res, next) => {
    logger.error('Unhandled error', { error: error.message, stack: error.stack });
    res.status(500).json({
        success: false,
        error: 'Internal server error',
        timestamp: new Date().toISOString()
    });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({
        success: false,
        error: 'Endpoint not found',
        availableEndpoints: [
            'GET /health',
            'GET /info', 
            'POST /trade',
            'POST /trade/batch'
        ]
    });
});

// Start server
app.listen(PORT, () => {
    logger.info(`🚀 MEXC Futures Trader Service started on port ${PORT}`);
    logger.info(`📊 Health check: http://localhost:${PORT}/health`);
    logger.info(`📋 Service info: http://localhost:${PORT}/info`);
    logger.info(`⚡ Ready to execute trades!`);
});

module.exports = app;
