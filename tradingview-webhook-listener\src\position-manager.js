const EventEmitter = require('events');

class PositionManager extends EventEmitter {
    constructor(configManager, marketDataService, tradingExecutor, logger) {
        super();
        this.configManager = configManager;
        this.marketDataService = marketDataService;
        this.tradingExecutor = tradingExecutor;
        this.logger = logger;
        
        this.positions = new Map(); // Store active positions
        this.monitoringInterval = null;
        this.monitoringFrequency = 1000; // Check every 1 second
        this.isMonitoring = false;
    }

    updateConfig(configManager, marketDataService, tradingExecutor) {
        this.configManager = configManager;
        this.marketDataService = marketDataService;
        this.tradingExecutor = tradingExecutor;
    }

    async addPosition(positionData) {
        const config = this.configManager.getConfig();
        
        if (!config.slTpEnabled) {
            this.logger.info('SL/TP disabled, not adding position for monitoring');
            return null;
        }

        try {
            // Get current ATR for SL/TP calculation
            const atr = await this.marketDataService.getATR(positionData.symbol);
            
            // Calculate SL/TP levels
            const slTpLevels = this.marketDataService.calculateSLTP(
                positionData.entryPrice,
                atr,
                config,
                positionData.direction || 'long'
            );

            const position = {
                id: positionData.id || `pos_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                symbol: positionData.symbol,
                direction: positionData.direction || 'long',
                entryPrice: positionData.entryPrice,
                quantity: positionData.quantity,
                originalQuantity: positionData.quantity,
                entryTime: new Date().toISOString(),
                
                // SL/TP Data
                atr: atr,
                stopLoss: slTpLevels.stopLoss,
                takeProfits: slTpLevels.takeProfits,
                trailingStart: slTpLevels.trailingStart,
                
                // Status
                status: 'active',
                isTrailing: false,
                trailingStopLoss: null,
                highestPrice: positionData.entryPrice, // For trailing
                lowestPrice: positionData.entryPrice, // For short trailing
                slType: slTpLevels.slType || 'Normal',
                moveToTPsActive: false,
                currentSLLevel: 'initial', // initial, tp1, tp2, tp3, entry
                
                // Execution tracking
                tpExecutions: [],
                lastPriceCheck: null,
                
                // Metadata
                tradeId: positionData.tradeId,
                requestId: positionData.requestId
            };

            this.positions.set(position.id, position);
            
            this.logger.info('Position added for monitoring', {
                positionId: position.id,
                symbol: position.symbol,
                entryPrice: position.entryPrice,
                stopLoss: position.stopLoss,
                takeProfits: position.takeProfits.length,
                atr: position.atr
            });

            // Start monitoring if not already running
            this.startMonitoring();
            
            this.emit('positionAdded', position);
            return position;
            
        } catch (error) {
            this.logger.error('Failed to add position for monitoring', {
                error: error.message,
                positionData
            });
            throw error;
        }
    }

    startMonitoring() {
        if (this.isMonitoring) return;
        
        this.isMonitoring = true;
        this.monitoringInterval = setInterval(() => {
            this.checkPositions();
        }, this.monitoringFrequency);
        
        this.logger.info('Position monitoring started');
    }

    stopMonitoring() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        this.isMonitoring = false;
        this.logger.info('Position monitoring stopped');
    }

    async checkPositions() {
        if (this.positions.size === 0) {
            this.stopMonitoring();
            return;
        }

        for (const [positionId, position] of this.positions) {
            try {
                await this.checkPosition(position);
            } catch (error) {
                this.logger.error('Error checking position', {
                    positionId,
                    error: error.message
                });
            }
        }
    }

    async checkPosition(position) {
        try {
            // Get current market price
            const priceData = await this.marketDataService.getCurrentPrice(position.symbol);
            const currentPrice = priceData.price;
            
            position.lastPriceCheck = new Date().toISOString();
            
            // Update highest/lowest price for trailing
            if (position.direction === 'long' && currentPrice > position.highestPrice) {
                position.highestPrice = currentPrice;

                // Update trailing stop loss if trailing is active
                if (position.isTrailing) {
                    const config = this.configManager.getConfig();
                    const trailingDistance = position.atr * config.trailingValue;
                    position.trailingStopLoss = position.highestPrice - trailingDistance;
                }
            } else if (position.direction === 'short' && currentPrice < position.lowestPrice) {
                position.lowestPrice = currentPrice;

                // Update trailing stop loss for short if trailing is active
                if (position.isTrailing) {
                    const config = this.configManager.getConfig();
                    const trailingDistance = position.atr * config.trailingValue;
                    position.trailingStopLoss = position.lowestPrice + trailingDistance;
                }
            }

            // Handle Move to TPs logic
            if (position.slType === 'MoveToTPs') {
                await this.handleMoveToTPs(position, currentPrice);
            }

            // Check if trailing should start
            if (!position.isTrailing && position.trailingStart && currentPrice >= position.trailingStart) {
                position.isTrailing = true;
                const config = this.configManager.getConfig();
                const trailingDistance = position.atr * config.trailingValue;
                position.trailingStopLoss = position.highestPrice - trailingDistance;
                
                this.logger.info('Trailing stop loss activated', {
                    positionId: position.id,
                    currentPrice,
                    trailingStart: position.trailingStart,
                    trailingStopLoss: position.trailingStopLoss
                });
            }

            // Check Stop Loss
            const stopLossPrice = position.isTrailing ? position.trailingStopLoss : position.stopLoss;
            const shouldTriggerSL = position.direction === 'long'
                ? currentPrice <= stopLossPrice
                : currentPrice >= stopLossPrice;

            if (shouldTriggerSL) {
                await this.executeStopLoss(position, currentPrice);
                return;
            }

            // Check Take Profits
            for (const tp of position.takeProfits) {
                if (!tp.enabled) continue;
                
                const alreadyExecuted = position.tpExecutions.find(exec => exec.level === tp.level);
                if (alreadyExecuted) continue;

                const shouldTriggerTP = position.direction === 'long'
                    ? currentPrice >= tp.price
                    : currentPrice <= tp.price;

                if (shouldTriggerTP) {
                    await this.executeTakeProfit(position, tp, currentPrice);
                }
            }
            
        } catch (error) {
            this.logger.error('Error in position check', {
                positionId: position.id,
                error: error.message
            });
        }
    }

    async executeStopLoss(position, currentPrice) {
        try {
            this.logger.info('Executing stop loss', {
                positionId: position.id,
                currentPrice,
                stopLoss: position.isTrailing ? position.trailingStopLoss : position.stopLoss,
                quantity: position.quantity
            });

            // Execute close trade with correct direction
            const orderType = position.direction === 'long' ? 'Close Long' : 'Close Short';
            const closeResult = await this.tradingExecutor.executeTrade({
                orderType,
                quantity: position.quantity.toString(),
                reason: 'stop_loss',
                positionId: position.id
            });

            if (closeResult.success) {
                position.status = 'closed_sl';
                position.closePrice = currentPrice;
                position.closeTime = new Date().toISOString();
                position.closeReason = 'stop_loss';
                
                this.logger.info('Stop loss executed successfully', {
                    positionId: position.id,
                    closePrice: currentPrice,
                    pnl: this.calculatePnL(position, currentPrice)
                });
                
                this.emit('stopLossExecuted', position, closeResult);
                this.positions.delete(position.id);
            } else {
                this.logger.error('Stop loss execution failed', {
                    positionId: position.id,
                    error: closeResult.error
                });
            }
            
        } catch (error) {
            this.logger.error('Stop loss execution error', {
                positionId: position.id,
                error: error.message
            });
        }
    }

    async executeTakeProfit(position, tp, currentPrice) {
        try {
            const closeQuantity = (position.quantity * tp.percent / 100);
            
            this.logger.info('Executing take profit', {
                positionId: position.id,
                level: tp.level,
                currentPrice,
                targetPrice: tp.price,
                closeQuantity,
                percent: tp.percent
            });

            // Execute partial close with correct direction
            const orderType = position.direction === 'long' ? 'Close Long' : 'Close Short';
            const closeResult = await this.tradingExecutor.executeTrade({
                orderType,
                quantity: closeQuantity.toString(),
                reason: `take_profit_${tp.level}`,
                positionId: position.id
            });

            if (closeResult.success) {
                // Record TP execution
                position.tpExecutions.push({
                    level: tp.level,
                    price: currentPrice,
                    quantity: closeQuantity,
                    time: new Date().toISOString(),
                    result: closeResult
                });
                
                // Update remaining quantity
                position.quantity -= closeQuantity;
                
                this.logger.info('Take profit executed successfully', {
                    positionId: position.id,
                    level: tp.level,
                    closePrice: currentPrice,
                    remainingQuantity: position.quantity
                });
                
                this.emit('takeProfitExecuted', position, tp, closeResult);
                
                // Close position if no quantity remaining
                if (position.quantity <= 0.0001) { // Small threshold for floating point
                    position.status = 'closed_tp';
                    position.closeTime = new Date().toISOString();
                    position.closeReason = 'take_profit_complete';
                    this.positions.delete(position.id);
                }
            } else {
                this.logger.error('Take profit execution failed', {
                    positionId: position.id,
                    level: tp.level,
                    error: closeResult.error
                });
            }
            
        } catch (error) {
            this.logger.error('Take profit execution error', {
                positionId: position.id,
                level: tp.level,
                error: error.message
            });
        }
    }

    async handleMoveToTPs(position, currentPrice) {
        const config = this.configManager.getConfig();

        // Sort TPs by level to check in order
        const sortedTPs = position.takeProfits.sort((a, b) => a.level - b.level);

        for (const tp of sortedTPs) {
            const alreadyExecuted = position.tpExecutions.find(exec => exec.level === tp.level);
            if (alreadyExecuted) continue;

            const tpReached = position.direction === 'long'
                ? currentPrice >= tp.price
                : currentPrice <= tp.price;

            if (tpReached) {
                // Move SL based on TP level
                let newSL;

                if (tp.level === 1) {
                    // At TP1, move SL to entry
                    newSL = position.entryPrice;
                    position.currentSLLevel = 'entry';
                } else if (tp.level === 2) {
                    // At TP2, move SL to TP1
                    const tp1 = position.takeProfits.find(t => t.level === 1);
                    if (tp1) {
                        newSL = tp1.price;
                        position.currentSLLevel = 'tp1';
                    }
                } else if (tp.level === 3) {
                    // At TP3, move SL to TP2
                    const tp2 = position.takeProfits.find(t => t.level === 2);
                    if (tp2) {
                        newSL = tp2.price;
                        position.currentSLLevel = 'tp2';
                    }
                }

                if (newSL !== undefined) {
                    position.stopLoss = newSL;
                    this.logger.info('Move to TPs: SL updated', {
                        positionId: position.id,
                        tpLevel: tp.level,
                        newSL,
                        currentSLLevel: position.currentSLLevel
                    });
                }

                break; // Only process the first reached TP
            }
        }
    }

    calculatePnL(position, currentPrice) {
        if (position.direction === 'long') {
            return (currentPrice - position.entryPrice) * position.originalQuantity;
        } else {
            return (position.entryPrice - currentPrice) * position.originalQuantity;
        }
    }

    getActivePositions() {
        return Array.from(this.positions.values());
    }

    getPosition(positionId) {
        return this.positions.get(positionId);
    }

    removePosition(positionId) {
        const removed = this.positions.delete(positionId);
        if (removed) {
            this.logger.info('Position removed from monitoring', { positionId });
            this.emit('positionRemoved', positionId);
        }
        return removed;
    }

    getStatistics() {
        return {
            activePositions: this.positions.size,
            isMonitoring: this.isMonitoring,
            monitoringFrequency: this.monitoringFrequency,
            positions: this.getActivePositions().map(pos => ({
                id: pos.id,
                symbol: pos.symbol,
                entryPrice: pos.entryPrice,
                quantity: pos.quantity,
                status: pos.status,
                isTrailing: pos.isTrailing,
                tpExecutions: pos.tpExecutions.length
            }))
        };
    }
}

module.exports = PositionManager;
